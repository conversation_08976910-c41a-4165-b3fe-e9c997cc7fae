<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">18%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-03 11:52 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0___init___py.html">src\hsi_pipeline\__init__.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py.html#t18">src\hsi_pipeline\create_consolidated_webodm_poses.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py.html#t18"><data value='run_consolidation'>run_consolidation</data></a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py.html">src\hsi_pipeline\create_consolidated_webodm_poses.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>17</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html#t14">src\hsi_pipeline\create_georeferenced_rgb.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html#t14"><data value='find_nearest_band_index'>find_nearest_band_index</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html#t20">src\hsi_pipeline\create_georeferenced_rgb.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html#t20"><data value='normalize_band'>normalize_band</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html#t58">src\hsi_pipeline\create_georeferenced_rgb.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html#t58"><data value='run_create_rgb_geotiff'>run_create_rgb_geotiff</data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html">src\hsi_pipeline\create_georeferenced_rgb.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t48">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t48"><data value='parse_hsi_header'>parse_hsi_header</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t128">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t128"><data value='parse_sensor_model'>parse_sensor_model</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t195">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t195"><data value='get_dsm_height_at_point'>get_dsm_height_at_point</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t228">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t228"><data value='create_ray_dsm_difference_function'>create_ray_dsm_difference_function</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t242">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t242"><data value='func_to_solve'>create_ray_dsm_difference_function.func_to_solve</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t256">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t256"><data value='find_dsm_entry_point'>find_dsm_entry_point</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t281">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t281"><data value='calculate_ray_dsm_intersection'>calculate_ray_dsm_intersection</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t378">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html#t378"><data value='run_georeferencing'>run_georeferencing</data></a></td>
                <td>254</td>
                <td>254</td>
                <td>0</td>
                <td class="right" data-ratio="0 254">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>17</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_lever_arm_utils_py.html#t16">src\hsi_pipeline\lever_arm_utils.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_lever_arm_utils_py.html#t16"><data value='determine_effective_lever_arm'>determine_effective_lever_arm</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_lever_arm_utils_py.html#t83">src\hsi_pipeline\lever_arm_utils.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_lever_arm_utils_py.html#t83"><data value='validate_lever_arm'>validate_lever_arm</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_lever_arm_utils_py.html">src\hsi_pipeline\lever_arm_utils.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_lever_arm_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_logging_config_py.html#t14">src\hsi_pipeline\logging_config.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_logging_config_py.html#t14"><data value='setup_logging'>setup_logging</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_logging_config_py.html#t75">src\hsi_pipeline\logging_config.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_logging_config_py.html#t75"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_logging_config_py.html">src\hsi_pipeline\logging_config.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_logging_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_main_pipeline_py.html#t39">src\hsi_pipeline\main_pipeline.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_main_pipeline_py.html#t39"><data value='load_pipeline_config'>load_pipeline_config</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_main_pipeline_py.html#t69">src\hsi_pipeline\main_pipeline.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_main_pipeline_py.html#t69"><data value='run_complete_pipeline'>run_complete_pipeline</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_main_pipeline_py.html">src\hsi_pipeline\main_pipeline.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_main_pipeline_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8___init___py.html">src\hsi_pipeline\plotting\__init__.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t8">src\hsi_pipeline\plotting\plot_hsi_data.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t8"><data value='plot_positions'>plot_positions</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t24">src\hsi_pipeline\plotting\plot_hsi_data.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t24"><data value='plot_trajectory_2d'>plot_trajectory_2d</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t38">src\hsi_pipeline\plotting\plot_hsi_data.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t38"><data value='plot_orientations'>plot_orientations</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t55">src\hsi_pipeline\plotting\plot_hsi_data.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t55"><data value='plot_interpolation_quality'>plot_interpolation_quality</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t70">src\hsi_pipeline\plotting\plot_hsi_data.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t70"><data value='plot_yaw_and_flight_direction'>plot_yaw_and_flight_direction</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t108">src\hsi_pipeline\plotting\plot_hsi_data.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html#t108"><data value='run_plotting'>run_plotting</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html">src\hsi_pipeline\plotting\plot_hsi_data.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t23">src\hsi_pipeline\synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t23"><data value='parse_hdr_file'>parse_hdr_file</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t74">src\hsi_pipeline\synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t74"><data value='convert_hsi_timestamp_to_ns'>convert_hsi_timestamp_to_ns</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t87">src\hsi_pipeline\synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t87"><data value='load_hsi_data'>load_hsi_data</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t167">src\hsi_pipeline\synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t167"><data value='load_webodm_data'>load_webodm_data</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t240">src\hsi_pipeline\synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t240"><data value='interpolate_pose'>interpolate_pose</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t337">src\hsi_pipeline\synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html#t337"><data value='run_synchronization'>run_synchronization</data></a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html">src\hsi_pipeline\synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>17</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t37">src\hsi_pipeline\vectorized_georef.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t37"><data value='calculate_sensor_view_vectors_vectorized'>calculate_sensor_view_vectors_vectorized</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t85">src\hsi_pipeline\vectorized_georef.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t85"><data value='transform_to_world_coordinates_vectorized'>transform_to_world_coordinates_vectorized</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t106">src\hsi_pipeline\vectorized_georef.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t106"><data value='calculate_flat_plane_intersections_vectorized'>calculate_flat_plane_intersections_vectorized</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t156">src\hsi_pipeline\vectorized_georef.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t156"><data value='calculate_dsm_intersections_vectorized'>calculate_dsm_intersections_vectorized</data></a></td>
                <td>60</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="57 60">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t298">src\hsi_pipeline\vectorized_georef.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html#t298"><data value='process_hsi_line_vectorized'>process_hsi_line_vectorized</data></a></td>
                <td>28</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="20 28">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html">src\hsi_pipeline\vectorized_georef.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1351</td>
                <td>1110</td>
                <td>77</td>
                <td class="right" data-ratio="241 1351">18%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-03 11:52 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
