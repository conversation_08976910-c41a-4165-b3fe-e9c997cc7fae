"""
Unit tests for HSI-WebODM synchronization functionality.

This module tests the synchronization logic as specified in LS7_3 requirements.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock

# Import the modules to test
from src.hsi_pipeline.synchronize_hsi_webodm import (
    parse_hdr_file, convert_hsi_timestamp_to_ns, load_hsi_data,
    load_webodm_data, interpolate_pose, run_synchronization
)


class TestParseHdrFile:
    """Test cases for parse_hdr_file function (LS7_3)."""

    def test_parse_hdr_file_valid_content(self, tmp_path):
        """Test parsing a valid HDR file."""
        # Arrange
        hdr_file = tmp_path / "test.hdr"
        with open(hdr_file, "w") as f:
            f.write("lines = 100\n")
            f.write("OffsetBetweenMainAntennaAndTargetPoint (x,y,z) = {10, 20, 30}\n")

        # Act
        metadata = parse_hdr_file(str(hdr_file))

        # Assert
        assert metadata is not None
        assert metadata["lines"] == 100
        assert metadata["lever_arm_xyz_mm"] == [10, 20, 30]

    def test_parse_hdr_file_missing_lines(self, tmp_path):
        """Test parsing HDR file without lines field."""
        # Arrange
        hdr_file = tmp_path / "test.hdr"
        with open(hdr_file, "w") as f:
            f.write("samples = 640\n")
            f.write("bands = 224\n")

        # Act
        metadata = parse_hdr_file(str(hdr_file))

        # Assert
        assert metadata is not None
        assert "lines" not in metadata

    def test_parse_hdr_file_not_found(self):
        """Test behavior when HDR file doesn't exist."""
        # Arrange
        nonexistent_file = "/path/to/nonexistent.hdr"

        # Act & Assert
        from src.hsi_pipeline.pipeline_exceptions import InputDataError
        with pytest.raises(InputDataError):
            parse_hdr_file(nonexistent_file)


class TestConvertHsiTimestamp:
    """Test cases for convert_hsi_timestamp_to_ns function (LS7_3)."""

    def test_convert_hsi_timestamp_to_ns_basic(self):
        """Test basic timestamp conversion."""
        # Arrange
        timestamp_str = "1000"  # 1000 microseconds
        expected_ns = 1000000  # 1000 * 1000 nanoseconds

        # Act
        result_ns = convert_hsi_timestamp_to_ns(timestamp_str)

        # Assert
        assert result_ns == expected_ns

    def test_convert_hsi_timestamp_to_ns_zero(self):
        """Test conversion of zero timestamp."""
        # Arrange
        timestamp_str = "0"
        expected_ns = 0

        # Act
        result_ns = convert_hsi_timestamp_to_ns(timestamp_str)

        # Assert
        assert result_ns == expected_ns

    def test_convert_hsi_timestamp_to_ns_large_value(self):
        """Test conversion of large timestamp."""
        # Arrange
        timestamp_str = "1234567890"
        expected_ns = 1234567890000

        # Act
        result_ns = convert_hsi_timestamp_to_ns(timestamp_str)

        # Assert
        assert result_ns == expected_ns


class TestInterpolatePose:
    """Test cases for interpolate_pose function (LS7_3)."""

    def test_interpolate_pose_empty_poses(self):
        """Test interpolation with empty pose list."""
        # Arrange
        hsi_timestamp_ns = 1000000
        webodm_poses = []

        # Act
        translation, rotation, delta_prev, delta_next = interpolate_pose(hsi_timestamp_ns, webodm_poses)

        # Assert
        assert translation is None
        assert rotation is None
        assert np.isnan(delta_prev)
        assert np.isnan(delta_next)

    def test_interpolate_pose_exact_match(self):
        """Test interpolation with exact timestamp match."""
        # Arrange
        hsi_timestamp_ns = 2000000
        webodm_poses = [
            {
                "timestamp_ns": 1000000,
                "translation": np.array([1.0, 2.0, 3.0]),
                "rotation_vec": np.array([0.1, 0.2, 0.3])
            },
            {
                "timestamp_ns": 2000000,
                "translation": np.array([4.0, 5.0, 6.0]),
                "rotation_vec": np.array([0.4, 0.5, 0.6])
            }
        ]

        # Act
        translation, rotation, delta_prev, delta_next = interpolate_pose(hsi_timestamp_ns, webodm_poses)

        # Assert
        assert translation is not None
        np.testing.assert_allclose(translation, np.array([4.0, 5.0, 6.0]))
        assert rotation is not None
        assert delta_prev == 0.0  # Exact match

    def test_interpolate_pose_before_all_timestamps(self):
        """Test interpolation when HSI timestamp is before all WebODM timestamps."""
        # Arrange
        hsi_timestamp_ns = 500000
        webodm_poses = [
            {
                "timestamp_ns": 1000000,
                "translation": np.array([1.0, 2.0, 3.0]),
                "rotation_vec": np.array([0.1, 0.2, 0.3])
            }
        ]

        # Act
        translation, rotation, delta_prev, delta_next = interpolate_pose(hsi_timestamp_ns, webodm_poses)

        # Assert
        assert translation is not None
        np.testing.assert_allclose(translation, np.array([1.0, 2.0, 3.0]))
        assert rotation is not None
        assert np.isnan(delta_prev)
        assert delta_next == 500000.0


class TestRunSynchronization:
    """Test cases for run_synchronization function (LS7_3)."""
    
    def test_run_synchronization_basic_functionality(self, mocker, tmp_path):
        """Test basic synchronization functionality."""
        # Arrange
        hsi_dir = tmp_path / "hsi"
        hsi_dir.mkdir()
        output_dir = tmp_path / "output"
        output_dir.mkdir()

        # Create required files
        hsi_hdr_file = hsi_dir / "test_hsi.hdr"
        hsi_sync_file = hsi_dir / "test_hsi_sync.txt"
        webodm_csv_file = output_dir / "webodm_poses.csv"

        # Create header file with required content
        hsi_hdr_file.write_text("lines = 100\nsamples = 200\n")

        # Create sync file with proper format
        hsi_sync_file.write_text("Frame/Line Timestamp\n100 10000000000\n99 20100000000\n98 30000000000\n")

        config = {
            'paths': {
                'hsi_data_directory': str(hsi_dir),
                'hsi_base_filename': 'test_hsi',
                'output_directory': str(output_dir),
                'hsi_poses_csv': 'synchronized_poses.csv',
                'consolidated_webodm_poses_csv': 'webodm_poses.csv'
            },
            'parameters': {
                'synchronization': {
                    'time_threshold_seconds': 0.1
                }
            }
        }



        # Create WebODM CSV file with proper format
        webodm_csv_file.write_text("haip_timestamp_ns,pos_x,pos_y,pos_z,rot_1,rot_2,rot_3\n10050000000,1.0,4.0,7.0,0.0,0.0,0.0\n25000000000,2.0,5.0,8.0,0.0,0.0,0.0\n30010000000,3.0,6.0,9.0,0.0,0.0,0.0\n")

        # Mock only the output CSV writing
        mock_to_csv = mocker.patch('pandas.DataFrame.to_csv')

        # Act
        result = run_synchronization(config)

        # Assert
        assert result is True
        mock_to_csv.assert_called_once()
    
    def test_run_synchronization_with_time_threshold(self, mocker, tmp_path):
        """Test synchronization respects time threshold."""
        # Arrange
        hsi_dir = tmp_path / "hsi"
        hsi_dir.mkdir()
        output_dir = tmp_path / "output"
        output_dir.mkdir()

        # Create required files
        hsi_hdr_file = hsi_dir / "test_hsi.hdr"
        hsi_sync_file = hsi_dir / "test_hsi_sync.txt"
        webodm_csv_file = output_dir / "webodm_poses.csv"

        # Create header file with required content
        hsi_hdr_file.write_text("lines = 100\nsamples = 200\n")

        # Create sync file with proper format
        hsi_sync_file.write_text("Frame/Line Timestamp\n100 10000000000\n99 20000000000\n98 30000000000\n")

        config = {
            'paths': {
                'hsi_data_directory': str(hsi_dir),
                'hsi_base_filename': 'test_hsi',
                'output_directory': str(output_dir),
                'hsi_poses_csv': 'synchronized_poses.csv',
                'consolidated_webodm_poses_csv': 'webodm_poses.csv'
            },
            'parameters': {
                'synchronization': {
                    'time_threshold_seconds': 0.05  # Strict threshold
                }
            }
        }



        # Create WebODM CSV file with proper format
        webodm_csv_file.write_text("haip_timestamp_ns,pos_x,pos_y,pos_z,rot_1,rot_2,rot_3\n10020000000,1.0,4.0,7.0,0.0,0.0,0.0\n20100000000,2.0,5.0,8.0,0.0,0.0,0.0\n30010000000,3.0,6.0,9.0,0.0,0.0,0.0\n")

        # Mock only the output CSV writing
        mock_to_csv = mocker.patch('pandas.DataFrame.to_csv')

        # Act
        result = run_synchronization(config)

        # Assert
        assert result is True
        mock_to_csv.assert_called_once()
    
    def test_run_synchronization_missing_config_keys(self, mocker):
        """Test error handling for missing configuration keys."""
        # Arrange
        incomplete_config = {
            'paths': {
                'hsi_data_directory': '/test/hsi'
                # Missing other required keys
            }
        }
        
        # Act & Assert
        from src.hsi_pipeline.pipeline_exceptions import PipelineConfigError
        with pytest.raises(PipelineConfigError):
            run_synchronization(incomplete_config)
    
    def test_run_synchronization_file_not_found(self, mocker, tmp_path):
        """Test error handling when input files are not found."""
        # Arrange
        config = {
            'paths': {
                'hsi_data_directory': str(tmp_path / "nonexistent"),
                'hsi_base_filename': 'test_hsi',
                'output_directory': str(tmp_path / "output"),
                'hsi_poses_csv': 'synchronized_poses.csv',
                'consolidated_webodm_poses_csv': 'webodm_poses.csv'
            },
            'parameters': {
                'synchronization': {
                    'time_threshold_seconds': 0.1
                }
            }
        }
        
        # Mock file operations to raise FileNotFoundError
        mocker.patch('src.hsi_pipeline.synchronize_hsi_webodm.pd.read_csv', side_effect=FileNotFoundError("File not found"))
        
        # Act & Assert
        from src.hsi_pipeline.pipeline_exceptions import InputDataError
        with pytest.raises(InputDataError):
            run_synchronization(config)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
