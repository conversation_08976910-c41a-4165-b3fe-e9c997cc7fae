{"test_synchronize_hsi_webodm.py::TestParseHdrFile": true, "test_synchronize_hsi_webodm.py::TestConvertHsiTimestamp": true, "test_synchronize_hsi_webodm.py::TestInterpolatePose": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_basic_functionality": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_with_time_threshold": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_missing_config_keys": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_file_not_found": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_identity": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_90deg_x_rotation": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_calculate_sensor_pixel_vectors_vectorized_boresight_center": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_transform_vectors_to_world_vectorized_identity_rotations": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_nadir_pointing": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_ray_parallel_to_plane": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_process_hsi_line_vectorized_flat_plane_nadir_simple": true}